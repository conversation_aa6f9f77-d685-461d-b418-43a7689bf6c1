#!/usr/bin/env python3
import pymysql
from pymysql.err import OperationalError, ProgrammingError
from datetime import datetime, timedelta

def query_flight_by_id(foc_flight_id, departure_alternates=None, arrival_alternates=None):
    # 数据库配置
    config = {
        'host': '************',
        'port': 3306,
        'user': 'hxyr_test',
        'password': 'yU6)RLLqa2b',
        'db': 'g5air_nfoc_adjust_test_db',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }

    connection = None
    try:
        # 建立数据库连接
        connection = pymysql.connect(**config)
        print(f"成功连接到数据库: {config['db']}")

        # 执行查询
        with connection.cursor() as cursor:
            sql = """
                SELECT 
                    foc_flight_id, icao_ac_type, ac_no, flight_no, flight_date, 
                    dep_icao, arr_icao, std, sta, flight_status 
                FROM flight 
                WHERE foc_flight_id = %s
            """
            cursor.execute(sql, (foc_flight_id,))
            # 打印执行的SQL语句
            # print(f"执行SQL: {cursor.mogrify(sql, (foc_flight_id,))}")
            result = cursor.fetchone()

            if result:
                print("\n查询结果:\n")
                for key, value in result.items():
                    print(f"{key}: {value}")
            else:
                print(f"\n未找到foc_flight_id为 {foc_flight_id} 的航班记录")
                return

            # 查询第二个数据库的通告信息
            query_ais_notam(
                flight_data=result,
                departure_alternates=departure_alternates or [],
                arrival_alternates=arrival_alternates or [],
                db_config={
                    'host': 'hxmysqltest1.g5air.com',
                    'port': 3308,
                    'user': 'hx_ais_prev',
                    'password': 'A^vkDYB8cyU',
                    'db': 'hx_ais_prev_db',
                    'charset': 'utf8mb4',
                    'cursorclass': pymysql.cursors.DictCursor
                }
            )

    except OperationalError as e:
        print(f"数据库连接错误: {str(e)}")
    except ProgrammingError as e:
        print(f"SQL执行错误: {str(e)}")
    except Exception as e:
        print(f"发生错误: {str(e)}")
    finally:
        # 关闭连接
        if connection and connection.open:
            connection.close()
            print("\n数据库连接已关闭")




def parse_notam_time(time_str):
    """将NOTAM时间格式(2208310000)转换为datetime对象"""
    if not time_str or len(time_str) < 10:
        return None
    try:
        year = 2000 + int(time_str[0:2])
        month = int(time_str[2:4])
        day = int(time_str[4:6])
        hour = int(time_str[6:8])
        minute = int(time_str[8:10])
        return datetime(year, month, day, hour, minute)
    except ValueError:
        print(f"时间格式解析错误: {time_str}")
        return None


def parse_snowtam_runway_times(rwy_desc, current_year=None):
    """解析雪情通告跑道描述中的时间信息，返回最早和最晚时间"""
    if not rwy_desc:
        return None, None

    if current_year is None:
        current_year = datetime.now().year

    times = []
    lines = rwy_desc.strip().split('\n')

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 查找时间格式 MMDDHHMM (8位数字)
        parts = line.split()
        for part in parts:
            if len(part) == 8 and part.isdigit():
                try:
                    month = int(part[0:2])
                    day = int(part[2:4])
                    hour = int(part[4:6])
                    minute = int(part[6:8])

                    # 处理跨年情况
                    year = current_year
                    if month > 12:  # 如果月份大于12，可能是年份信息错误
                        continue

                    # 创建datetime对象
                    dt = datetime(year, month, day, hour, minute)

                    # 检查是否需要调整年份（处理跨年情况）
                    now = datetime.now()
                    if dt > now + timedelta(days=180):  # 如果时间超过当前时间6个月，可能是去年的
                        dt = datetime(year - 1, month, day, hour, minute)
                    elif dt < now - timedelta(days=180):  # 如果时间早于当前时间6个月，可能是明年的
                        dt = datetime(year + 1, month, day, hour, minute)

                    times.append(dt)
                except ValueError:
                    continue

    if not times:
        return None, None

    earliest_time = min(times)
    latest_time = max(times) + timedelta(hours=8)  # 最晚时间加8小时

    return earliest_time, latest_time


def query_ais_notam(flight_data, departure_alternates, arrival_alternates, db_config):
    """查询与航班相关的NOTAM通告"""
    try:
        # 提取航班关键信息
        std = flight_data['std']
        if isinstance(std, str):
            std = datetime.strptime(std, '%Y-%m-%d %H:%M:%S')
        
        sta = flight_data.get('sta')
        if sta and isinstance(sta, str):
            sta = datetime.strptime(sta, '%Y-%m-%d %H:%M:%S')
        dep_icao = flight_data['dep_icao']
        arr_icao = flight_data['arr_icao']

        # 计算时间范围
        time_start = std - timedelta(hours=24)
        time_end = sta + timedelta(hours=48) if sta else std + timedelta(hours=72)

        print(f"\n查询NOTAM时间范围: {time_start} 至 {time_end}")
        print(f"匹配机场: {dep_icao}, {arr_icao}")

        # 构建所有需要查询的机场列表
        all_airports = [dep_icao, arr_icao] + departure_alternates + arrival_alternates
        # 去重并过滤空值
        unique_airports = list(set([airport for airport in all_airports if airport]))

        print(f"\n查询机场列表: {unique_airports}")
        print(f"起飞机场: {dep_icao}")
        print(f"落地机场: {arr_icao}")
        if departure_alternates:
            print(f"起飞备降场: {', '.join(departure_alternates)}")
        if arrival_alternates:
            print(f"落地备降场: {', '.join(arrival_alternates)}")

        # 连接第二个数据库
        connection = pymysql.connect(**db_config)
        with connection.cursor() as cursor:
            # 动态构建IN子句的占位符
            airport_placeholders = ', '.join(['%s'] * len(unique_airports))

            # 查询符合条件的NOTAM和公司通告
            sql = f"""
            SELECT
                series_no, telex_type, notice_level, item_a, item_b, item_c, item_e, `range`,
                'NOTAM' as notice_source  -- 标记为航行通告
            FROM ais_notam
            WHERE
                -- 时间交集条件：判断通告有效期与查询区间有重叠
                (
                    (item_c = '' AND item_b <= %s) OR  -- 永久通告：通告开始时间 <= 查询结束时间
                    (item_c != '' AND item_b <= %s AND item_c >= %s)  -- 非永久通告：通告区间与查询区间重叠
                )
                AND item_a IN ({airport_placeholders})
                AND `range` IN ('A', 'AE', 'AW', 'E', 'W')
                AND is_valid = 'Y'
            UNION ALL
            SELECT
                series_no, telex_type, notice_level, item_a, item_b, item_c, item_e, `range`,
                'COTAM' as notice_source  -- 标记为公司通告
            FROM ais_cotam
            WHERE
                -- 时间交集条件：判断通告有效期与查询区间有重叠
                (
                    (item_c = '' AND item_b <= %s) OR  -- 永久通告：通告开始时间 <= 查询结束时间
                    (item_c != '' AND item_b <= %s AND item_c >= %s)  -- 非永久通告：通告区间与查询区间重叠
                )
                AND item_a IN ({airport_placeholders})
                AND `range` IN ('A', 'AE', 'AW', 'E', 'W')
                AND is_valid = 'Y'
            ORDER BY notice_source, series_no  -- 先按通告来源排序，再按编号排序
            """

            # 解析时间参数
            query_end = time_end.strftime('%y%m%d%H%M')  # 查询结束时间
            query_start = time_start.strftime('%y%m%d%H%M')  # 查询开始时间

            # 构建参数列表
            params = [
                query_end,  # 用于永久通告判断 (item_c='' 时)
                query_end,  # 用于非永久通告结束时间判断
                query_start,  # 用于非永久通告开始时间判断
            ] + unique_airports + [  # 第一个查询的机场列表
                query_end,  # 用于永久通告判断 (item_c='' 时)
                query_end,  # 用于非永久通告结束时间判断
                query_start,  # 用于非永久通告开始时间判断
            ] + unique_airports  # 第二个查询的机场列表

            cursor.execute(sql, params)
            # 打印执行的SQL语句
            # print(f"执行SQL: {cursor.mogrify(sql, (item_b_start, item_c_end, dep_icao, arr_icao))}")
            notam_results = cursor.fetchall()

            # 查询雪情通告
            snowtam_sql = f"""
            SELECT
                swaa_no, item_a, observ_time, telex_type, rwy_desc,
                'SNOWTAM' as notice_source  -- 标记为雪情通告
            FROM ais_snowtam
            WHERE
                item_a IN ({airport_placeholders})
                AND is_valid = 'Y'
            ORDER BY swaa_no
            """

            cursor.execute(snowtam_sql, unique_airports)
            snowtam_raw_results = cursor.fetchall()

            # 过滤雪情通告：检查跑道时间是否与查询时间范围重叠
            snowtam_results = []
            for snowtam in snowtam_raw_results:
                earliest_time, latest_time = parse_snowtam_runway_times(snowtam['rwy_desc'])
                if earliest_time and latest_time:
                    # 检查时间是否重叠
                    if not (latest_time < time_start or earliest_time > time_end):
                        snowtam_results.append(snowtam)

            # 按机场类型和维度分组展示NOTAM结果
        if notam_results or snowtam_results:
            # 合并所有通告结果
            all_results = notam_results + snowtam_results

            # 按起飞/落地机场分组
            dep_notams = [n for n in all_results if n['item_a'] == flight_data['dep_icao']]
            arr_notams = [n for n in all_results if n['item_a'] == flight_data['arr_icao']]

            # 按备降场分组
            dep_alternate_notams = {}
            for airport in departure_alternates:
                dep_alternate_notams[airport] = [n for n in all_results if n['item_a'] == airport]

            arr_alternate_notams = {}
            for airport in arrival_alternates:
                arr_alternate_notams[airport] = [n for n in all_results if n['item_a'] == airport]

            # 判断机场是否为国内机场
            def is_domestic_airport(icao_code):
                return len(icao_code) == 4 and icao_code.startswith('Z') and icao_code[1] not in ('M', 'K')

            # 按机场/情报区维度分组
            def group_by_type(notams_list):
                airport = [n for n in notams_list if n.get('range') in ('A', 'AE', 'AW')]
                intel_area = [n for n in notams_list if n.get('range') in ('E', 'W')]
                snowtam = [n for n in notams_list if n.get('notice_source') == 'SNOWTAM']
                return {'机场': airport, '情报区': intel_area, '雪情': snowtam}

            # 显示通告的通用函数
            def display_notams(airport_code, notams_list, airport_type=""):
                if not notams_list:
                    return

                groups = group_by_type(notams_list)
                is_domestic = is_domestic_airport(airport_code)

                print(f"\n{'-'*80}")
                print(f"===== {airport_type}{airport_code} ({'国内' if is_domestic else '国外'}) 相关通告 =====")
                print(f"{'-'*80}")

                for type_name, type_notams in groups.items():
                    if type_notams:
                        if type_name == '雪情':
                            # 雪情通告特殊处理
                            snowtam_count = sum(1 for n in type_notams if n['notice_source'] == 'SNOWTAM')
                            print(f"\n{type_name}通告 (共{len(type_notams)}条, 雪情通告{snowtam_count}条):")

                            for n in type_notams:
                                if n['notice_source'] == 'SNOWTAM':
                                    print("  -------------------------------------")
                                    print(f"  {n['swaa_no']} {n['telex_type']} - [雪情通告]")
                                    print(f"  机场: {n['item_a']}")
                                    print(f"  观测时间: {n['observ_time']}")
                                    print(f"  跑道描述: {n['rwy_desc'][:100]}...")  # 只显示前100个字符

                                    # 解析并显示跑道时间范围
                                    earliest_time, latest_time = parse_snowtam_runway_times(n['rwy_desc'])
                                    if earliest_time and latest_time:
                                        print(f"  有效时间范围: {earliest_time} 至 {latest_time}")
                                    print("  -------------------------------------")
                        else:
                            # 普通通告处理
                            chinese_count = sum(1 for n in type_notams if (type_name == '机场' and (n.get('series_no', '').startswith('C') or n.get('series_no', '').startswith('K'))) or \
                                               (type_name == '情报区' and n.get('series_no', '').startswith('C')))
                            english_count = len(type_notams) - chinese_count

                            # 统计航行通告和公司通告数量
                            notam_count = sum(1 for n in type_notams if n['notice_source'] == 'NOTAM')
                            cotam_count = sum(1 for n in type_notams if n['notice_source'] == 'COTAM')

                            print(f"\n{type_name}通告 (共{len(type_notams)}条, 中文{chinese_count}条, 英文{english_count}条, 航行通告{notam_count}条, 公司通告{cotam_count}条):")

                            # 先显示中文通告
                            print(f"\n中文通告 (共{chinese_count}条):")
                            for n in type_notams:
                                if n['notice_source'] in ['NOTAM', 'COTAM'] and \
                                   ((type_name == '机场' and (n.get('series_no', '').startswith('C') or n.get('series_no', '').startswith('K'))) or \
                                   (type_name == '情报区' and n.get('series_no', '').startswith('C'))):
                                    notice_source_cn = "航行通告" if n['notice_source'] == 'NOTAM' else "公司通告"
                                    print(f"  {n['series_no']} {n['telex_type']} (中文) - [{notice_source_cn}]")
                                    print(f"  A){n['item_a']}")
                                    print(f"  B){n['item_b']} C) {'永久有效' if not n['item_c'] else n['item_c']}")
                                    print(f"  E){n['item_e'][0:50]}")
                                    print(f"  通告级别: {n['notice_level']}")
                                    print("  -------------------------------------")

                            # 再显示英文通告
                            print(f"\n英文通告 (共{english_count}条):")
                            for n in type_notams:
                                if n['notice_source'] in ['NOTAM', 'COTAM'] and \
                                   not ((type_name == '机场' and (n.get('series_no', '').startswith('C') or n.get('series_no', '').startswith('K'))) or \
                                       (type_name == '情报区' and n.get('series_no', '').startswith('C'))):
                                    notice_source_cn = "航行通告" if n['notice_source'] == 'NOTAM' else "公司通告"
                                    print(f"  {n['series_no']} {n['telex_type']} (英文) - [{notice_source_cn}]")
                                    print(f"  A){n['item_a']}")
                                    print(f"  B){n['item_b']} C) {'永久有效' if not n['item_c'] else n['item_c']}")
                                    print(f"  E){n['item_e'][0:50]}")
                                    print(f"  通告级别: {n['notice_level']}")
                                    print("  -------------------------------------")

            # 显示起飞机场通告
            display_notams(flight_data['dep_icao'], dep_notams, "起飞机场 ")

            # 显示起飞备降场通告
            for airport in departure_alternates:
                if dep_alternate_notams[airport]:
                    display_notams(airport, dep_alternate_notams[airport], "起飞备降场 ")

            # 显示落地机场通告
            display_notams(flight_data['arr_icao'], arr_notams, "落地机场 ")

            # 显示落地备降场通告
            for airport in arrival_alternates:
                if arr_alternate_notams[airport]:
                    display_notams(airport, arr_alternate_notams[airport], "落地备降场 ")

    except OperationalError as e:
        print(f"通告数据库连接错误: {str(e)}")
    except Exception as e:
        print(f"通告查询错误: {str(e)}")
    finally:
        if 'connection' in locals() and connection.open:
            connection.close()

if __name__ == '__main__':
    try:
        # 直接在代码中指定foc_flight_id和备降场
        foc_flight_id = 1521241  # 可根据需要修改此值

        # 起飞备降场列表（可以输入多个）
        departure_alternate_airports = []  # 可根据需要修改

        # 落地备降场列表（可以输入多个）
        arrival_alternate_airports = []  # 可根据需要修改

        query_flight_by_id(foc_flight_id, departure_alternate_airports, arrival_alternate_airports)
    except Exception as e:
        print(f"发生错误: {e}")
    except KeyboardInterrupt:
        print("\n程序已取消")