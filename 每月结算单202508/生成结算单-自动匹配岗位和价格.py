# -*- coding: utf-8 -*-
import os
import openpyxl
import pandas as pd
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH  # 导入居中对齐常量
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

# 获取当前目录
current_directory = os.getcwd()

from datetime import datetime


def set_cell_borders(cell, color="A9A9A9", width="4"):
    """设置单元格的边框样式"""
    tc_pr = cell._element.get_or_add_tcPr()
    borders = OxmlElement("w:tcBorders")

    for border_name in ["top", "left", "bottom", "right"]:
        border = OxmlElement(f"w:{border_name}")
        border.set(qn("w:val"), "single")  # 实线
        border.set(qn("w:sz"), width)  # 宽度
        border.set(qn("w:space"), "0")  # 无间距
        border.set(qn("w:color"), color)  # 边框颜色
        borders.append(border)

    tc_pr.append(borders)


def get_current_and_next_month(today=None):
    # 如果 today 为 None，则默认为上个月的当天
    if today is None:
        today = datetime.today()
        # 计算上个月的日期
        if today.month == 1:
            today = today.replace(year=today.year - 1, month=12)
        else:
            today = today.replace(month=today.month - 1)

    # 获取当前年月（格式为 "2024 年 12 月"）
    current_year_month = today.strftime("%Y 年 %m 月")

    # 计算下月的年月
    # 如果当前是12月，下一月为下一年的1月
    next_month_date = today.replace(month=today.month % 12 + 1, year=today.year + (today.month // 12))
    next_year_month = next_month_date.strftime("%Y 年 %m 月")
    current_year_month_fomart = today.strftime("%Y年%m月")

    return current_year_month, next_year_month, current_year_month_fomart


# 示例：调用函数
current, next_month, current_fomart = get_current_and_next_month()

# 遍历当前目录中的所有文件
for filename in os.listdir(current_directory):
    # 只处理 .xlsx 文件
    if filename.endswith('.xlsx'):
        file_path = os.path.join(current_directory, filename)

        # 打开 Excel 文件
        workbook = openpyxl.load_workbook(file_path, data_only=True)  # data_only=True 获取公式的计算结果

        # 检查是否有名为 "实际计算单汇总表" 的工作表
        if "实际结算单汇总表" in workbook.sheetnames:
            sheet = workbook["实际结算单汇总表"]
            # 将工作表内容转换为 pandas DataFrame
            data = sheet.values
            columns = next(data)  # 获取第一行作为列名
            df = pd.DataFrame(data, columns=columns)

            # 读取"当月人员清单"sheet的数据
            if "当月人员清单" in workbook.sheetnames:
                personnel_sheet = workbook["当月人员清单"]
                personnel_data = personnel_sheet.values
                personnel_columns = next(personnel_data)  # 获取第一行作为列名
                personnel_df = pd.DataFrame(personnel_data, columns=personnel_columns)

                # 确保数据框包含所需的列
                if "姓名" in personnel_df.columns and "岗位等级" in personnel_df.columns:
                    # 使用merge更新主表的岗位等级
                    df = df.merge(personnel_df[["姓名", "岗位等级"]],
                                  on="姓名",
                                  how="left",
                                  suffixes=("", "_new"))

                    # 更新岗位等级：如果新的岗位等级存在，则使用新的，否则保留原来的
                    df["岗位等级"] = df["岗位等级_new"].fillna(df["岗位等级"])

                    # 删除多余的列
                    if "岗位等级_new" in df.columns:
                        df = df.drop(columns=["岗位等级_new"])
                else:
                    print("警告：当月人员清单中缺少必要的列（姓名、岗位等级）")
            else:
                print("警告：未找到'当月人员清单'工作表")

            # 定义岗位等级对应的单价
            price_mapping = {
                "中级UI工程师": 1000,
                "中级测试工程师": 1000,
                "高级测试工程师": 1400,
                "中级后端开发工程师": 1050,
                "高级后端开发工程师": 1400,
                "中级前端开发工程师": 1050,
                "高级前端开发工程师": 1400,
                "运维工程师": 1400,
                "中级算法工程师": 1500,
                "高级算法工程师": 1800,
                "中级产品经理": 1300,
                "高级产品经理": 1500
            }

            # 根据岗位等级更新单价
            df["单价"] = df["岗位等级"].map(price_mapping)
            # 重新计算结算金额
            df["华夏航空结算金额（元）"] = df["华夏航空结算人天"] * df["单价"]
            print(df.to_string())
            df_cleaned = df.dropna(
                subset=["项目名称", "岗位等级", "华夏航空结算人天", "华夏航空结算金额（元）", "使用部门", "项目经理"])
            df_unique = df[['项目名称', '使用部门', '项目经理']].drop_duplicates()
            excel_name = f'{current_fomart}结算清单'
            df1 = df[["姓名", "岗位等级", "单价", "华夏航空结算人天", "考勤数据人天", "华夏航空结算金额（元）"]]
            # 重命名列
            df1 = df1.rename(columns={"华夏航空结算人天": "结算人天", "华夏航空结算金额（元）": "结算金额（元）"})
            df1['考勤数据人天'] = df1['考勤数据人天'].round(1)
            df1 = df1.dropna(subset=["姓名"])
            # 合并相同人员的数据
            df1 = df1.groupby(["姓名", "岗位等级", "单价"]).agg({
                "结算人天": "sum",
                "考勤数据人天": "sum",
                "结算金额（元）": "sum"
            }).reset_index()

            # 按姓名排序
            df1 = df1.sort_values(by=["姓名"])
            # 计算总计行
            total_row = pd.DataFrame({
                "姓名": [""],
                "岗位等级": [""],
                "单价": [""],
                "结算人天": [df1["结算人天"].sum()],
                "考勤数据人天": [df1["考勤数据人天"].sum()],
                "结算金额（元）": [df1["结算金额（元）"].sum()]
            })

            # 将总计行添加到df1
            df1 = pd.concat([df1, total_row], ignore_index=True)

            # 保存为Excel文件
            # output_path = os.path.join(current_directory, f"{excel_name}.xlsx")
            output_path = f"./结算单/{excel_name}.xlsx"
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df1.to_excel(writer, sheet_name=excel_name, index=False, startrow=1)  # 从第2行开始写入数据

                # 获取工作簿和工作表
                workbook = writer.book
                worksheet = writer.sheets[excel_name]

                # 添加标题并合并单元格
                worksheet.merge_cells(f'A1:{openpyxl.utils.get_column_letter(len(df1.columns))}1')
                title_cell = worksheet['A1']
                title_cell.value = excel_name
                title_cell.font = openpyxl.styles.Font(bold=True, size=14)
                title_cell.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')

                # 设置列宽 - 增加宽度确保不会缩在一起
                for i, col in enumerate(df1.columns):
                    column_width = max(len(str(col)), df1[col].astype(str).map(len).max()) + 12  # 增加宽度
                    worksheet.column_dimensions[openpyxl.utils.get_column_letter(i + 1)].width = column_width

                # 设置标题行格式（现在是第2行）
                for cell in worksheet[2]:
                    cell.font = openpyxl.styles.Font(bold=True)
                    cell.alignment = openpyxl.styles.Alignment(horizontal='center')

                # 设置数值格式和边框（需要调整行号，因为添加了标题）
                thin_border = openpyxl.styles.Border(
                    left=openpyxl.styles.Side(style='thin'),
                    right=openpyxl.styles.Side(style='thin'),
                    top=openpyxl.styles.Side(style='thin'),
                    bottom=openpyxl.styles.Side(style='thin')
                )

                # 为标题行添加边框
                for cell in worksheet[2]:
                    cell.border = thin_border

                # 为标题单元格添加边框
                title_cell.border = thin_border

                # 为数据行添加边框和格式
                for row in range(3, len(df1) + 3):  # 数据从第3行开始
                    for col_idx, col_name in enumerate(df1.columns):
                        cell = worksheet.cell(row=row, column=col_idx + 1)
                        if col_name in ["结算人天", "考勤数据人天"]:
                            cell.number_format = '0.0'
                        elif col_name == "结算金额（元）":
                            cell.number_format = '#,##0'

                        # 居中对齐
                        cell.alignment = openpyxl.styles.Alignment(horizontal='center')
                        # 添加边框
                        cell.border = thin_border

                # 设置最后一行（总计行）的格式
                last_row = len(df1) + 2  # 因为添加了标题行，所以要加2
                for col_idx, col_name in enumerate(df1.columns):
                    cell = worksheet.cell(row=last_row, column=col_idx + 1)
                    if col_name in ["结算人天", "考勤数据人天", "结算金额（元）"]:
                        cell.font = openpyxl.styles.Font(bold=True)
                    # 添加边框
                    cell.border = thin_border

                # 调整行高
                worksheet.row_dimensions[1].height = 30  # 标题行高度
                for row in range(2, len(df1) + 3):
                    worksheet.row_dimensions[row].height = 20  # 数据行高度

            print(f"已将结算清单保存为: {output_path}")
            # 查看结果
            for row in df_unique.itertuples(index=False):
                doc = Document('template.docx')
                项目名称 = row.项目名称
                使用部门 = row.使用部门
                项目经理 = row.项目经理
                if 项目名称 is not None:
                    filtered_df = df[df['项目名称'] == 项目名称]

                    # 查看结果
                    # print(filtered_df.to_string())
                    结算单 = (
                        filtered_df.groupby(["项目名称", "岗位等级"])["华夏航空结算人天"]
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 按项目名称和人数排序
                    )
                    # print(结算单)
                    jsd_list = []
                    for jsd in 结算单.itertuples(index=False):
                        gwdj = jsd.岗位等级
                        jsrt = jsd.华夏航空结算人天
                        jsd_list.append(["岗位", gwdj, "工作量", f"{jsrt}人天"])

                    # 定位到目标表格
                    table_with_titles = None
                    # 遍历文档中的所有表格
                    for table in doc.tables:
                        # 遍历表格中的所有行
                        for i, row in enumerate(table.rows):
                            # 检查每一行是否包含“时间范围”
                            for cell in row.cells:
                                if '时间范围' in cell.text:
                                    table_with_titles = table
                                    break
                    if not table_with_titles:
                        raise ValueError("未找到包含指定标题的表格！")

                    gzzl = (
                        filtered_df.groupby(["项目名称", "岗位等级", "姓名"])["华夏航空结算人天"]
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 按项目名称和人数排序
                    )

                    gzzl_list = []
                    for gzzl in gzzl.itertuples(index=False):
                        gwdj = gzzl.岗位等级
                        xm = gzzl.姓名
                        jsrt = gzzl.华夏航空结算人天
                        gzzl_list.append([gwdj, xm, str(jsrt)])
                    # print(gzzl_list)
                    table_with_titles_1 = None
                    for table in doc.tables:
                        first_row_text = [cell.text.strip() for cell in table.rows[0].cells]
                        # print(first_row_text)
                        if "岗位" in first_row_text and "工作人员" in first_row_text and "工作量（人/天）" in first_row_text:
                            table_with_titles_1 = table
                            break

                    if not table_with_titles_1:
                        raise ValueError("未找到包含指定标题的表格！")
                    # 确定插入行的位置（例如在第2行和第3行之间插入，索引从0开始）
                    insert_position_1 = 2  # 表示在第2行之后插入新行（第3行之前）

                    # # 创建新行
                    new_row_data = gzzl_list
                    for data_row in new_row_data:
                        # 创建新行
                        new_row = table_with_titles_1.add_row()
                        for i, cell in enumerate(new_row.cells):
                            if i < len(data_row):  # 防止超出数据范围
                                cell.text = data_row[i]
                                # 设置字体样式为宋体、字号为11号，内容居中
                                for run in cell.paragraphs[0].runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(10)
                                    # 添加间距设置
                                    paragraph = cell.paragraphs[0]
                                    paragraph_format = paragraph.paragraph_format
                                    paragraph_format.space_before = Pt(6)  # 段前间距
                                    paragraph_format.space_after = Pt(6)  # 段后间距
                                    paragraph_format.line_spacing = 1  # 行距为1.5倍
                                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                                set_cell_borders(cell)
                            # 调整新行到目标位置
                        tbl = table_with_titles_1._tbl  # 获取底层 XML 表格对象
                        rows = tbl.xpath("./w:tr")  # 获取所有行
                        new_row_xml = rows[-1]  # 新插入的行为最后一行
                        tbl.remove(new_row_xml)  # 删除新行
                        tbl.insert(insert_position_1 + 1, new_row_xml)  # 插入到目标位置之后

                        # 插入完成后，更新目标位置索引
                        insert_position_1 += 1
                    work_summary_df = (
                        filtered_df.groupby(["项目名称", "岗位等级"])[
                            ["华夏航空结算人天", "华夏航空结算金额（元）"]]  # 使用列表
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 排序
                    )
                    # print(work_summary)
                    work_summary_list = []
                    work_summary_count = 0
                    work_summary_count_jsrt = 0
                    for work_summary in work_summary_df.itertuples(index=False):
                        gwdj = work_summary.岗位等级
                        jsrt = work_summary.华夏航空结算人天
                        jsje = work_summary[-1]
                        work_summary_count = work_summary_count + jsje
                        work_summary_count_jsrt = work_summary_count_jsrt + jsrt
                        # print(jsje)
                        work_summary_list.append([gwdj, str(jsrt), str(jsje)])
                    # print(gzzl_list)
                    table_with_titles_2 = None
                    for table in doc.tables:
                        first_row_text = [cell.text.strip() for cell in table.rows[0].cells]
                        # print(first_row_text)
                        if "岗位" in first_row_text and "预付服务金额（元）" in first_row_text and "工作量（人/天）" in first_row_text:
                            table_with_titles_2 = table
                            break

                    if not table_with_titles_2:
                        raise ValueError("未找到包含指定标题的表格！")
                    # 确定插入行的位置（例如在第2行和第3行之间插入，索引从0开始）
                    insert_position_2 = 2  # 表示在第2行之后插入新行（第3行之前）

                    # # 创建新行
                    new_row_data = work_summary_list
                    # print(new_row_data)
                    for data_row in new_row_data:
                        # 创建新行
                        new_row = table_with_titles_2.add_row()
                        for i, cell in enumerate(new_row.cells):
                            if i < len(data_row):  # 防止超出数据范围
                                cell.text = data_row[i]
                                # 设置字体样式为宋体、字号为11号，内容居中
                                for run in cell.paragraphs[0].runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(10)
                                    # 添加间距设置
                                    paragraph = cell.paragraphs[0]
                                    paragraph_format = paragraph.paragraph_format
                                    paragraph_format.space_before = Pt(6)  # 段前间距
                                    paragraph_format.space_after = Pt(6)  # 段后间距
                                    paragraph_format.line_spacing = 1  # 行距为1.5倍
                                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                                set_cell_borders(cell)
                            # 调整新行到目标位置
                        tbl = table_with_titles_2._tbl  # 获取底层 XML 表格对象
                        rows = tbl.xpath("./w:tr")  # 获取所有行
                        new_row_xml = rows[-1]  # 新插入的行为最后一行
                        tbl.remove(new_row_xml)  # 删除新行
                        tbl.insert(insert_position_2 + 1, new_row_xml)  # 插入到目标位置之后

                        # 插入完成后，更新目标位置索引
                        insert_position_2 += 1

                    # 直接在第一个表格的第七行开始插入数据
                    table_with_titles_3 = None
                    if doc.tables:
                        table_with_titles_3 = doc.tables[0]  # 使用第一个表格

                    if not table_with_titles_3:
                        raise ValueError("文档中没有找到表格！")

                    # 设置插入位置为第七行（索引6，从0开始计数）
                    insert_position_3 = 8  # 第七行位置

                    # 检查表格的列数，如果超过4列，我们需要特殊处理
                    if len(table_with_titles_3.rows) > 0:
                        original_cols = len(table_with_titles_3.rows[0].cells)
                        print(f"原表格列数: {original_cols}")

                    # # 创建新行 - 使用现有数据构造"岗位\岗位数据\工作量\工作量数据"格式
                    new_row_data = work_summary_list
                    # print(new_row_data)
                    for data_row in new_row_data:
                        # 从现有数据中提取岗位等级和结算人天
                        gwdj = data_row[0]  # 岗位等级
                        jsrt = data_row[1]  # 结算人天

                        # 创建新行
                        new_row = table_with_titles_3.add_row()

                        # 获取新行的总列数
                        total_cols = len(new_row.cells)

                        # 填充数据到前4列，确保格式正确
                        cell_data = ["岗位", gwdj, "工作量", jsrt]

                        # 只填充前4列，其余列保持空白但不删除
                        for i in range(min(4, total_cols)):
                            cell = new_row.cells[i]
                            cell.text = cell_data[i]

                            # 设置单元格格式
                            for paragraph in cell.paragraphs:
                                # 清除现有的运行，重新设置
                                paragraph.clear()
                                run = paragraph.add_run(cell_data[i])
                                run.font.name = '宋体'
                                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                run.font.size = Pt(10)

                                # 设置段落格式
                                paragraph_format = paragraph.paragraph_format
                                paragraph_format.space_before = Pt(6)
                                paragraph_format.space_after = Pt(6)
                                paragraph_format.line_spacing = 1
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                            # 设置边框
                            set_cell_borders(cell)

                        # 清空多余列的内容，但保持列结构
                        for i in range(4, total_cols):
                            cell = new_row.cells[i]
                            cell.text = ""
                            # 也给空列设置边框，保持表格完整性
                            set_cell_borders(cell)

                        # 设置所有单元格的格式
                        for i in range(min(4, total_cols)):
                            cell = new_row.cells[i]
                            # 设置字体样式为宋体、字号为11号，内容居中
                            for paragraph in cell.paragraphs:
                                for run in paragraph.runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(10)
                                # 添加间距设置
                                paragraph_format = paragraph.paragraph_format
                                paragraph_format.space_before = Pt(6)  # 段前间距
                                paragraph_format.space_after = Pt(6)  # 段后间距
                                paragraph_format.line_spacing = 1  # 行距为1.5倍
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                            set_cell_borders(cell)

                        # 调整新行到目标位置
                        tbl = table_with_titles_3._tbl  # 获取底层 XML 表格对象
                        rows = tbl.xpath("./w:tr")  # 获取所有行
                        new_row_xml = rows[-1]  # 新插入的行为最后一行
                        tbl.remove(new_row_xml)  # 删除新行
                        tbl.insert(insert_position_3, new_row_xml)  # 插入到第七行位置

                        # 插入完成后，无需更新目标位置索引，所有数据都在第七行位置插入


                    填充数据 = {
                        "{项目名称}": 项目名称,
                        "{项目经理}": 项目经理,
                        "{使用部门}": 使用部门,
                        "{本月}": current,
                        "{下月}": next_month,
                        "{标题月份}": current_fomart,
                        "{总计}": work_summary_count,
                        "{人天}": round(work_summary_count_jsrt, 2)
                    }
                    # 遍历文档中的所有段落
                    for paragraph in doc.paragraphs:
                        # 遍历字典中的每个标签，进行替换
                        for 标签, 数据 in 填充数据.items():
                            if 标签 in paragraph.text:
                                # 替换标签为对应的数据
                                paragraph.text = paragraph.text.replace(标签, str(数据))
                                # 设置字体为宋体，字号为 10号
                                for run in paragraph.runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(15)
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    # 遍历文档中的所有表格
                    for table in doc.tables:
                        # 遍历每一行
                        for row in table.rows:
                            # 遍历每一行中的每个单元格
                            for cell in row.cells:
                                # 遍历字典中的每个标签，进行替换
                                for 标签, 数据 in 填充数据.items():
                                    if 标签 in cell.text:
                                        # 替换标签为对应的数据
                                        cell.text = cell.text.replace(标签, str(数据))
                                        # 设置字体为宋体，字号为 10号
                                        for paragraph in cell.paragraphs:
                                            for run in paragraph.runs:
                                                run.font.name = '宋体'
                                                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                                run.font.size = Pt(10)
                                                paragraph = cell.paragraphs[0]
                                                paragraph_format = paragraph.paragraph_format
                                                paragraph_format.space_before = Pt(6)  # 段前间距
                                                paragraph_format.space_after = Pt(6)  # 段后间距
                                                paragraph_format.line_spacing = 1  # 行距为1.5倍
                                            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for table in doc.tables:
                        for row in table.rows:
                            for cell in row.cells:
                                if "工作量确认" in cell.text or "供应商确认" in cell.text or "需求功能确认" in cell.text:
                                    # 设置该单元格所有段落为左对齐
                                    for paragraph in cell.paragraphs:
                                        paragraph_format = paragraph.paragraph_format
                                        paragraph_format.space_before = Pt(6)  # 段前间距
                                        paragraph_format.space_after = Pt(6)  # 段后间距
                                        paragraph_format.line_spacing = 1.5  # 行距为1.5倍
                                        paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                                    break

                    doc.save(f"./结算单/{项目名称}{current_fomart}结算单.docx")



        else:
            print(f"文件: {filename} 没有名为 '实际计算单汇总表' 的工作表.")
