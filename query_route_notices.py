#!/usr/bin/env python3
import pymysql
from pymysql.err import OperationalError, ProgrammingError
from datetime import datetime, timedelta


def parse_notam_time(time_str):
    """将NOTAM时间格式(2208310000)转换为datetime对象"""
    if not time_str or len(time_str) < 10:
        return None
    try:
        year = 2000 + int(time_str[0:2])
        month = int(time_str[2:4])
        day = int(time_str[4:6])
        hour = int(time_str[6:8])
        minute = int(time_str[8:10])
        return datetime(year, month, day, hour, minute)
    except ValueError:
        print(f"时间格式解析错误: {time_str}")
        return None


def parse_ashtam_time(time_str, current_year=None):
    """解析火山灰通告时间格式(08011030)，返回开始时间和结束时间(+24小时)"""
    if not time_str or len(time_str) < 8:
        return None, None
    
    if current_year is None:
        current_year = datetime.now().year
    
    try:
        month = int(time_str[0:2])
        day = int(time_str[2:4])
        hour = int(time_str[4:6])
        minute = int(time_str[6:8])
        
        # 处理跨年情况
        year = current_year
        if month > 12:
            return None, None
        
        # 创建datetime对象
        start_time = datetime(year, month, day, hour, minute)
        
        # 检查是否需要调整年份（处理跨年情况）
        now = datetime.now()
        if start_time > now + timedelta(days=180):  # 如果时间超过当前时间6个月，可能是去年的
            start_time = datetime(year - 1, month, day, hour, minute)
        elif start_time < now - timedelta(days=180):  # 如果时间早于当前时间6个月，可能是明年的
            start_time = datetime(year + 1, month, day, hour, minute)
        
        end_time = start_time + timedelta(hours=24)  # 有效期为开始时间+24小时
        
        return start_time, end_time
    except ValueError:
        print(f"火山灰通告时间格式解析错误: {time_str}")
        return None, None


def query_route_notices(dep_icao, arr_icao, time_start, time_end):
    """查询航路相关的通告"""
    # 数据库配置
    db_config = {
        'host': 'hxmysqltest1.g5air.com',
        'port': 3308,
        'user': 'hx_ais_prev',
        'password': 'A^vkDYB8cyU',
        'db': 'hx_ais_prev_db',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    connection = None
    try:
        print(f"\n{'='*80}")
        print(f"查询航路通告")
        print(f"起飞机场: {dep_icao}")
        print(f"落地机场: {arr_icao}")
        print(f"查询时间范围: {time_start} 至 {time_end}")
        print(f"{'='*80}")
        
        # 建立数据库连接
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 查询航路表
            route_sql = """
            SELECT notice_id, notice_type, dep4, arr4, suffix 
            FROM ais_route 
            WHERE dep4 = %s AND arr4 = %s
            """
            
            cursor.execute(route_sql, (dep_icao, arr_icao))
            route_results = cursor.fetchall()
            
            if not route_results:
                print(f"\n未找到从 {dep_icao} 到 {arr_icao} 的航路通告")
                return
            
            print(f"\n找到 {len(route_results)} 条航路记录:")
            for route in route_results:
                print(f"  Notice ID: {route['notice_id']}, Type: {route['notice_type']}, "
                      f"Route: {route['dep4']}-{route['arr4']}, Suffix: {route['suffix']}")
            
            # 统计各类型通告数量
            notam_count = 0
            cotam_count = 0
            ashtam_count = 0
            
            # 解析时间参数
            query_end = time_end.strftime('%y%m%d%H%M')
            query_start = time_start.strftime('%y%m%d%H%M')
            
            # 处理每条航路记录
            for route in route_results:
                notice_id = route['notice_id']
                notice_type = route['notice_type']
                
                print(f"\n{'-'*60}")
                print(f"处理航路通告 ID: {notice_id}, 类型: {notice_type}")
                print(f"航路: {route['dep4']}-{route['arr4']} ({route['suffix']})")
                print(f"{'-'*60}")
                
                if notice_type == 2:
                    # NOTAM 通告
                    notam_sql = """
                    SELECT series_no, item_e, item_b, item_c, is_valid
                    FROM ais_notam 
                    WHERE id = %s 
                        AND is_valid = 'Y'
                        AND (
                            (item_c = '' AND item_b <= %s) OR
                            (item_c != '' AND item_b <= %s AND item_c >= %s)
                        )
                    """
                    
                    cursor.execute(notam_sql, (notice_id, query_end, query_end, query_start))
                    notam_results = cursor.fetchall()
                    
                    if notam_results:
                        print(f"航行通告 (NOTAM) - 共 {len(notam_results)} 条:")
                        for notam in notam_results:
                            print(f"  编号: {notam['series_no']}")
                            print(f"  内容: {notam['item_e'][:100]}...")
                            print(f"  有效期: {notam['item_b']} - {'永久' if not notam['item_c'] else notam['item_c']}")
                            print("  " + "-" * 50)
                        notam_count += len(notam_results)
                    else:
                        print("航行通告 (NOTAM) - 无符合条件的通告")
                
                elif notice_type == 4:
                    # COTAM 通告
                    cotam_sql = """
                    SELECT series_no, item_e, item_b, item_c, is_valid
                    FROM ais_cotam 
                    WHERE id = %s 
                        AND is_valid = 'Y'
                        AND (
                            (item_c = '' AND item_b <= %s) OR
                            (item_c != '' AND item_b <= %s AND item_c >= %s)
                        )
                    """
                    
                    cursor.execute(cotam_sql, (notice_id, query_end, query_end, query_start))
                    cotam_results = cursor.fetchall()
                    
                    if cotam_results:
                        print(f"公司通告 (COTAM) - 共 {len(cotam_results)} 条:")
                        for cotam in cotam_results:
                            print(f"  编号: {cotam['series_no']}")
                            print(f"  内容: {cotam['item_e'][:100]}...")
                            print(f"  有效期: {cotam['item_b']} - {'永久' if not cotam['item_c'] else cotam['item_c']}")
                            print("  " + "-" * 50)
                        cotam_count += len(cotam_results)
                    else:
                        print("公司通告 (COTAM) - 无符合条件的通告")
                
                elif notice_type == 3:
                    # ASHTAM 火山灰通告
                    ashtam_sql = """
                    SELECT series_no, item_e, item_b, is_valid
                    FROM ais_ashtam 
                    WHERE id = %s AND is_valid = 'Y'
                    """
                    
                    cursor.execute(ashtam_sql, (notice_id,))
                    ashtam_raw_results = cursor.fetchall()
                    
                    # 过滤火山灰通告：检查时间是否与查询时间范围重叠
                    ashtam_results = []
                    for ashtam in ashtam_raw_results:
                        start_time, end_time = parse_ashtam_time(ashtam['item_b'])
                        if start_time and end_time:
                            # 检查时间是否重叠
                            if not (end_time < time_start or start_time > time_end):
                                ashtam_results.append({
                                    'series_no': ashtam['series_no'],
                                    'item_e': ashtam['item_e'],
                                    'item_b': ashtam['item_b'],
                                    'start_time': start_time,
                                    'end_time': end_time
                                })
                    
                    if ashtam_results:
                        print(f"火山灰通告 (ASHTAM) - 共 {len(ashtam_results)} 条:")
                        for ashtam in ashtam_results:
                            print(f"  编号: {ashtam['series_no']}")
                            print(f"  内容: {ashtam['item_e'][:100]}...")
                            print(f"  发布时间: {ashtam['item_b']}")
                            print(f"  有效期: {ashtam['start_time']} - {ashtam['end_time']}")
                            print("  " + "-" * 50)
                        ashtam_count += len(ashtam_results)
                    else:
                        print("火山灰通告 (ASHTAM) - 无符合条件的通告")
                
                else:
                    print(f"未知通告类型: {notice_type}")
            
            # 显示统计信息
            print(f"\n{'='*80}")
            print(f"航路通告统计汇总")
            print(f"{'='*80}")
            print(f"航行通告 (NOTAM): {notam_count} 条")
            print(f"公司通告 (COTAM): {cotam_count} 条")
            print(f"火山灰通告 (ASHTAM): {ashtam_count} 条")
            print(f"总计: {notam_count + cotam_count + ashtam_count} 条")
            print(f"{'='*80}")
    
    except OperationalError as e:
        print(f"数据库连接错误: {str(e)}")
    except Exception as e:
        print(f"查询错误: {str(e)}")
    finally:
        if connection and connection.open:
            connection.close()


def query_flight_and_route_notices(foc_flight_id):
    """查询航班信息并获取航路通告"""
    # 数据库配置
    flight_config = {
        'host': '************',
        'port': 3306,
        'user': 'hxyr_test',
        'password': 'yU6)RLLqa2b',
        'db': 'g5air_nfoc_adjust_test_db',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }

    connection = None
    try:
        # 建立数据库连接
        connection = pymysql.connect(**flight_config)
        print(f"成功连接到航班数据库: {flight_config['db']}")

        # 执行查询
        with connection.cursor() as cursor:
            sql = """
                SELECT 
                    foc_flight_id, icao_ac_type, ac_no, flight_no, flight_date, 
                    dep_icao, arr_icao, std, sta, flight_status 
                FROM flight 
                WHERE foc_flight_id = %s
            """
            cursor.execute(sql, (foc_flight_id,))
            result = cursor.fetchone()

            if result:
                print("\n航班信息:")
                for key, value in result.items():
                    print(f"{key}: {value}")
                
                # 提取航班关键信息
                std = result['std']
                if isinstance(std, str):
                    std = datetime.strptime(std, '%Y-%m-%d %H:%M:%S')
                
                sta = result.get('sta')
                if sta and isinstance(sta, str):
                    sta = datetime.strptime(sta, '%Y-%m-%d %H:%M:%S')
                
                dep_icao = result['dep_icao']
                arr_icao = result['arr_icao']

                # 计算时间范围
                time_start = std - timedelta(hours=24)
                time_end = sta + timedelta(hours=48) if sta else std + timedelta(hours=72)

                # 查询航路通告
                query_route_notices(dep_icao, arr_icao, time_start, time_end)
                
            else:
                print(f"\n未找到foc_flight_id为 {foc_flight_id} 的航班记录")

    except OperationalError as e:
        print(f"航班数据库连接错误: {str(e)}")
    except ProgrammingError as e:
        print(f"SQL执行错误: {str(e)}")
    except Exception as e:
        print(f"发生错误: {str(e)}")
    finally:
        # 关闭连接
        if connection and connection.open:
            connection.close()
            print("\n航班数据库连接已关闭")


if __name__ == '__main__':
    try:
        # 手动指定起飞和落地机场（优先使用）
        manual_dep_icao = "ZUCK"  # 起飞机场，留空则使用航班ID查询
        manual_arr_icao = "ZGCZ"  # 落地机场，留空则使用航班ID查询

        # 默认航班ID（当手动机场为空时使用）
        foc_flight_id = 1521241  # 可根据需要修改此值

        if manual_dep_icao and manual_arr_icao:
            # 使用手动输入的机场
            print(f"使用手动输入的机场: {manual_dep_icao} -> {manual_arr_icao}")

            # 使用航班ID获取时间范围
            flight_config = {
                'host': '************',
                'port': 3306,
                'user': 'hxyr_test',
                'password': 'yU6)RLLqa2b',
                'db': 'g5air_nfoc_adjust_test_db',
                'charset': 'utf8mb4',
                'cursorclass': pymysql.cursors.DictCursor
            }

            connection = None
            try:
                connection = pymysql.connect(**flight_config)
                with connection.cursor() as cursor:
                    sql = """
                        SELECT std, sta
                        FROM flight
                        WHERE foc_flight_id = %s
                    """
                    cursor.execute(sql, (foc_flight_id,))
                    result = cursor.fetchone()

                    if result:
                        std = result['std']
                        if isinstance(std, str):
                            std = datetime.strptime(std, '%Y-%m-%d %H:%M:%S')

                        sta = result.get('sta')
                        if sta and isinstance(sta, str):
                            sta = datetime.strptime(sta, '%Y-%m-%d %H:%M:%S')

                        # 计算时间范围
                        time_start = std - timedelta(hours=24)
                        time_end = sta + timedelta(hours=48) if sta else std + timedelta(hours=72)

                        print(f"使用航班ID {foc_flight_id} 的时间范围进行查询")

                        # 查询航路通告
                        query_route_notices(manual_dep_icao, manual_arr_icao, time_start, time_end)
                    else:
                        print(f"未找到航班ID {foc_flight_id}，无法获取时间范围")

            finally:
                if connection and connection.open:
                    connection.close()
        else:
            # 使用航班ID查询
            print(f"使用航班ID {foc_flight_id} 查询")
            query_flight_and_route_notices(foc_flight_id)

    except Exception as e:
        print(f"发生错误: {e}")
    except KeyboardInterrupt:
        print("\n程序已取消")
