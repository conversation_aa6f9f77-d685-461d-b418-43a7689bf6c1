from datetime import datetime

data = [['G52815', 'B650P', 'CKG', 'HET', 'CKG-HET', datetime(2023, 9, 26, 6, 35), datetime(2023, 9, 26, 9, 5)],
        ['G52633', 'B602P', 'HET', 'JIQ', 'HET-JIQ', datetime(2023, 9, 26, 18, 40), datetime(2023, 9, 26, 19, 30)],
        ['G52731', 'B650Q', 'CKG', 'HCZ', 'CKG-HCZ', datetime(2023, 9, 26, 14, 15), datetime(2023, 9, 26, 16, 15)],
        ['G54019', 'B3381', 'HCZ', 'LHW', 'HCZ-LHW', datetime(2023, 9, 26, 6, 35), datetime(2023, 9, 26, 8, 20)],
        ['G52725', 'B601Z', 'CKG', 'JIQ', 'CKG-JIQ', datetime(2023, 9, 26, 7, 50), datetime(2023, 9, 26, 9, 55)]]

# 对航线数据按起飞时间进行排序
sorted_data = sorted(data, key=lambda x: x[5])

# 初始化结果列表
route_combinations = []

# 遍历排序后的航线数据
for i in range(len(sorted_data) - 1):
    current_route = sorted_data[i]
    next_route = sorted_data[i + 1]

    # 检查是否满足条件：从CKG到JIQ并且后一条航线的起飞时间晚于前一条航线的落地时间
    if current_route[2] == 'CKG' and next_route[3] == 'JIQ' and current_route[6] < next_route[5]:
        route_combinations.append((current_route, next_route))

# 打印符合条件的航线组合
for combination in route_combinations:
    print("航班1:", combination[0][0])
    print("航班2:", combination[1][0])
    print("=========")
