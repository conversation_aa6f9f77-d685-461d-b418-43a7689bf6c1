# coding=utf-8
from ExecuteHana import ExecuteHana
from datetime import datetime, timedelta


def time_diff(start_time, end_time):
    """
    计算两个时间之间的时间差
    :param start_time: 开始时间，格式为 '%Y-%m-%d %H:%M:%S'
    :param end_time: 结束时间，格式为 '%Y-%m-%d %H:%M:%S'
    :return: 时间差，单位为秒
    """
    # start = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
    # end = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
    diff = (end_time - start_time).total_seconds()
    return diff


def day_dif(date_str1, date_str2):
    # 将日期字符串转换为 datetime 对象
    date1 = datetime.strptime(date_str1, '%Y-%m-%d')
    date2 = datetime.strptime(date_str2, '%Y-%m-%d')
    # 计算天数差异
    diff = (date2 - date1).days
    return diff - 1


def all_crew_id():
    mxb = ExecuteHana().excute_sql(
        f"SELECT distinct crew_id FROM BIDM.DM_F_CREW_TRIP_DETAIL where rq >='2023-01-01'")
    end_list = []
    for x in mxb:
        end_list.append(x[0])
    return end_list


def find_interval_time(crew_id):
    mxb = ExecuteHana().excute_sql(
        f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id} and a.type_class = '执飞航班' and a.rq >='2018-01-01' order by a.rq,a.begin_time")
    interval_list = []
    for x in range(len(mxb)):
        mxb_list = mxb[x]
        mxb_rq = mxb_list[0]
        mxb_crew_id = mxb_list[1]
        mxb_crew_name = mxb_list[2]
        mxb_type_class = mxb_list[3]
        mxb_begin_time = mxb_list[4]
        mxb_end_time = mxb_list[5]
        if x > 0:
            interval_time = (time_diff(mxb[x - 1][5], mxb_begin_time) / 3600)
            if interval_time >= 50:
                interval_list.append([mxb[x - 1][0], mxb_rq])

    return interval_list


def find_rule(crew_id, start_time, end_time):
    # xiangcha = day_dif(start_time, end_time)
    xxq = ExecuteHana().excute_sql(
        f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq >'{start_time}' and a.rq <'{end_time}' and a.type_class = '休息期' order by a.rq,a.begin_time")
    interval_list = []
    # 查找休息为1的情况
    if len(xxq) < 1:
        tf = ExecuteHana().excute_sql(
            f"SELECT distinct a.rq FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq >'{start_time}' and a.rq <'{end_time}' and type_class ='休假' ")
        if len(tf) > 0 and len(tf) < 2:
            interval_list.append([crew_id, start_time, end_time])

    return interval_list


# 计算实际飞行小时数
def cal_SJFXXS(crew_id, rq):
    sql = f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq like '%{rq}%' and type_class = '执飞航班' order by a.rq,a.begin_time"
    print(sql)
    mxb = ExecuteHana().excute_sql(sql)
    time_count = 0
    for x in mxb:
        mxb_rq = x[0]
        mxb_crew_id = x[1]
        mxb_crew_name = x[2]
        mxb_type_class = x[3]
        mxb_begin_time = x[4]
        mxb_end_time = x[5]
        tmp = time_diff(mxb_begin_time, mxb_end_time)
        time_count = time_count + tmp
    return round(time_count / 3600, 2)


# 模拟机实际带教检时间
def cal_MNJJJSC(crew_id, rq):
    mxb = ExecuteHana().excute_sql(
        f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq like '%{rq}%' and a.type_class like '%模拟%'  order by a.rq,a.begin_time")
    time_count = 0
    for x in mxb:
        tc = x[3]
        rq = x[0]
        mxb_begin_time = x[4]
        mxb_end_time = x[5]
        tmp = time_diff(mxb_begin_time, mxb_end_time)
        if tmp < 3600 * 4:
            tmp1 = tmp
            print(rq, tmp1)
            if tc != '模拟机-参训':
                time_count = time_count + tmp1
        if 3600 * 4 <= tmp < 3600 * 6:
            tmp1 = tmp - 15 * 60
            print(rq, tmp1)
            if tc != '模拟机-参训':
                time_count = time_count + tmp1
        if 3600 * 6 <= tmp < 3600 * 8:
            tmp1 = tmp - 30 * 60
            print(rq, tmp1)
            if tc != '模拟机-参训':
                time_count = time_count + tmp1
        if tmp >= 3600 * 8:
            tmp1 = tmp - 45 * 60
            print(rq, tmp1)
            if tc != '模拟机-参训':
                time_count = time_count + tmp1

    return round(time_count / 3600, 2)


# 数据清洗
def cal_yxj(crew_id, rq):
    sql = f"SELECT DISTINCT rq, crew_id, crew_name, type_class FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE rq like '%{rq}%'  AND crew_id = {crew_id} ORDER BY rq, CASE WHEN type_class = '执飞航班' THEN 1 WHEN type_class = '备份' THEN 2 WHEN type_class = '模拟机-检查员' THEN 3 WHEN type_class = '模拟机-教员' THEN 4 WHEN type_class = '模拟机-参训' THEN 5 WHEN type_class = '外围训练' THEN 6 WHEN type_class = '体检' THEN 7 WHEN type_class = '理论培训' THEN 8 WHEN type_class = '值班' THEN 9 WHEN type_class = '技评会' THEN 10 WHEN type_class = '置位' THEN 11 WHEN type_class = '休假' THEN 12 WHEN type_class = '停飞' THEN 13 WHEN type_class = '出差' THEN 14 WHEN type_class = '考试' THEN 15 WHEN type_class = '其他' THEN 16 WHEN type_class = '休息期' THEN 17 ELSE 18 END"
    mxb = ExecuteHana().excute_sql(sql)

    # 保留的日期和数据
    reserved_dates = set()
    reserved_data = []
    # 统计每个 type_class 类型的个数
    type_class_counts = {}
    # 处理查询结果
    for row in mxb:
        rq, crew_id, crew_name, type_class = row

        # 检查日期是否已保留
        if rq in reserved_dates:
            continue

        # 添加当前日期的数据
        reserved_data.append(row)
        reserved_dates.add(rq)
        # 统计 type_class 类型的个数
        if type_class in type_class_counts:
            type_class_counts[type_class] += 1
        else:
            type_class_counts[type_class] = 1

    # 输出结果
    for row in reserved_data:
        print(row)
    # 输出每个 type_class 类型的个数
    # for type_class, count in type_class_counts.items():
    #     print(f"{type_class}: {count}")

    return type_class_counts


if __name__ == '__main__':
    # 计算实际飞行小时数
    print(cal_SJFXXS(56, "2021-05"))

    # 模拟机实际带教检时间
    # print(cal_MNJJJSC(708, "2022-05"))
    # print(cal_yxj(503, "2022-11"))
    # 数据清洗
    print(cal_yxj(544, '2018-01'))








    # crew_id_list = all_crew_id()
    # for crew_id in crew_id_list:
    #     reslt = find_interval_time(crew_id)
    #     for x in reslt:
    #         a = find_rule(crew_id, x[0], x[1])
    #         if len(a) > 0:
    #             print(a)

    # date_str1 = '2021-09-06'
    # date_str2 = '2021-09-18'
    # b = day_dif(date_str1, date_str2)
    # print(b)
    # a = all_crew_id()
    # print(a)


