# -*- coding: utf-8 -*-
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

# Session = sessionmaker(bind=create_engine('mysql+pymysql://root:root@localhost:3306/data_base'))
Session = sessionmaker(bind=create_engine('mysql+pymysql://hxyr_test:yU6)RLLqa2b@************:3306/sf_floms_fpmp_test'))

session = Session()


def query_sql(sql_str):
    query = session.execute(text(sql_str))
    results = query.all()
    session.close()
    return results


if __name__ == '__main__':
    print(query_sql("select * from fpmp_index "))
