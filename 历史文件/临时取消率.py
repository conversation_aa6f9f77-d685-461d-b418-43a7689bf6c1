# -*- coding: utf-8 -*-

import pandas as pd
from connect_db import query_sql
from datetime import datetime, timedelta
from collections import defaultdict


def combination_n_2(n):
    return n * (n - 1) // 2

t= 1
def 获取航班(year, month):
    mysql = f"""
    SELECT
	flight_date,
    flight_no,
    legno,
    dep_iata,
    arr_iata,
    ori_arr_iata,
    std,
    flight_status,
    div_status,
    cnl_time
FROM
	fpmp_flight 
WHERE
	YEAR ( flight_date ) = {year} 
	AND MONTH ( flight_date ) = {month}
	AND DAY (flight_date) ={t}
ORDER BY legno
	"""
    fpmp_flight = query_sql(mysql)
    end_list = []
    for x in fpmp_flight:
        end_list.append([str(x[0]), x[1], x[2], x[3], x[4], x[5], str(x[6]), x[7], x[8], str(x[9])])
    return end_list


def 航段总数量(year, month):
    mysql = f"""
     SELECT
	a.flight_no,
	a.flight_date,
	count( a.air_line ) hx
    FROM
	fpmp_flight a 
    WHERE
	a.deleted = 0 
-- 	AND a.nation_flag = 0 
	AND YEAR ( flight_date ) = {year} 
	AND MONTH ( flight_date ) = {month}
	AND DAY (flight_date) ={t}
--     AND DAY(a.flight_date) = 1
	AND a.stc IN ( 'C', 'G', 'H', 'J' ) 
	AND a.legno LIKE '%00' 
    GROUP BY
	a.flight_date,
	a.flight_no
    ORDER BY
	a.flight_no
	"""
    jg = query_sql(mysql)
    end_count = 0
    for x in jg:
        flight_no, flight_date, hx = x
        end_count = end_count + combination_n_2(hx + 1)
    print(end_count)
    return end_count


def 无联程航班取消数量(year, month):
    mysql = f"""
         SELECT
    sub.flight_no,
    sub.flight_date,
    sub.air_line,
    sub.ori_arr_iata,
    sub.legno,
    sub.std,
    sub.flight_status,
    sub.div_status,
	sub.cnl_time,
    TIMESTAMPDIFF(SECOND, a.cnl_time, a.std)/3600 cz
FROM
(
    SELECT
        a.flight_no,
        a.flight_date,
        a.air_line,
        a.ori_arr_iata,
        a.legno,
        a.std,
        a.flight_status,
        a.div_status,
	    a.cnl_time,
        COUNT(DISTINCT a.foc_flight_id) AS sl
    FROM
        fpmp_flight a
    WHERE
        a.deleted = 0
--         AND a.nation_flag = 0
        AND YEAR(a.flight_date) = {year}
        AND MONTH(a.flight_date) = {month}
        AND DAY (flight_date) ={t}
--         AND DAY(a.flight_date) = 1
        AND a.stc IN ('C', 'G', 'H', 'J')
    GROUP BY
        a.flight_no,
        a.flight_date
		 
) sub
JOIN fpmp_flight a ON sub.flight_no = a.flight_no AND sub.flight_date = a.flight_date
WHERE
    TIMESTAMPDIFF(SECOND, a.cnl_time, a.std) <= 24*3600
    AND a.flight_status = 'CNL'
    AND sub.sl = 1
GROUP BY
    sub.flight_no,
    sub.flight_date,
    sub.sl
ORDER BY
    sub.flight_no;
    	"""
    jg = query_sql(mysql)
    zs = len(jg)
    # print(jg)
    df = pd.DataFrame(jg,
                      columns=['flight_no', 'flight_date', 'air_line', 'ori_arr_iata', 'legno', 'std', 'flight_status',
                               'div_status', 'cnl_time', 'time'])
    df.to_excel("非联乘.xlsx")
    # for x in jg:
    #     print(f"{str(x[1])}_{x[0]}_{x[2][0:3]}_{x[2][4:]}")
    #     # print(x)
    # print("============以上都为非联程航班=============")
    return zs


def 联程航班航班列表(year, month):
    mysql = f"""
         SELECT
	a.flight_no,
	a.flight_date
FROM
	fpmp_flight a 
WHERE
	a.deleted = 0 
-- 	AND a.nation_flag = 0 
	AND YEAR ( a.flight_date ) = {year} 
	AND MONTH ( a.flight_date ) = {month} 
	AND DAY(a.flight_date) ={t}
--     AND DAY(a.flight_date) = 1
	AND a.stc IN ( 'C', 'G', 'H', 'J' ) 
GROUP BY
	a.flight_no,
	a.flight_date 
HAVING
	COUNT( DISTINCT a.foc_flight_id ) > 1
ORDER BY
    a.flight_date
    	"""
    jg = query_sql(mysql)
    reslut = [[x[0], str(x[1])] for x in jg]
    return reslut


def 联程航班详情(year, month):
    end_list = []
    lc_flight = 联程航班航班列表(year, month)
    all_flght = 获取航班(year, month)
    for x in lc_flight:
        flight_no, flight_date = x
        tmp = []
        for y in all_flght:
            flight_no_y = y[1]
            flight_date_y = y[0]
            if flight_no == flight_no_y and flight_date == flight_date_y:
                tmp.append(y)
        end_list.append(tmp)
    return end_list


def cal_cz(std_time, cancel_time):
    cancel_time_finnal = datetime.strptime(cancel_time, '%Y-%m-%d %H:%M:%S')
    std_time_finnal = datetime.strptime(std_time, '%Y-%m-%d %H:%M:%S')
    time_diff = std_time_finnal - cancel_time_finnal
    seconds_diff = time_diff.total_seconds()
    if seconds_diff <= 24 * 3600:  # 24小时等于24 * 3600秒
        a = f"时间差值为: {round(seconds_diff / 3600, 2)} 小时，小于等于 24 小时"
        # print(a)
        return 1, a
    else:
        a = f"时间差值为: {round(seconds_diff / 3600, 2)} 小时，大于 24 小时"
        # print(f"时间差值为: {round(seconds_diff / 3600,2)} 小时，大于 24 小时")
        return 0, a


def 计算联程航班(year, month):
    org_info = 联程航班详情(year, month)
    count_flight = 0
    for flight_info in org_info:
        ori_line = []
        std_time = []
        cancel_line = []
        new_flight_info = merge_flight(flight_info)
        for t in flight_info:
            print(t)
        for x in new_flight_info:
            # print(x)
            flight_date, flight_no, legno, dep_iata, arr_iata, ori_arr_iata, std, flight_status, div_status, cnl_time = x
            std_time.append([dep_iata, std])
            if dep_iata not in ori_line:
                ori_line.append(dep_iata)
            if arr_iata not in ori_line:
                ori_line.append(arr_iata)
            if flight_status == 'CNL' and cnl_time != 'None':
                cancel_line.append([ori_arr_iata, cnl_time])
        # print(ori_line)
        # print(std_time)
        # print(cancel_line)
        if len(cancel_line) >= 1:
            for cancel_info in cancel_line:
                cancel_iata, cancel_time = cancel_info
                for std_info in std_time:
                    dep_iata, std = std_info
                    if cancel_iata != dep_iata:
                        jsjg = cal_cz(std, cancel_time)
                        if jsjg[0] > 0:
                            print(f"{flight_no}:{dep_iata}-{cancel_iata}  {jsjg[1]} ")
                            # print(f"{flight_date}_{flight_no}_{dep_iata}_{cancel_iata}")
                            # print(f"{flight_no}-{dep_iata}-{cancel_iata}")
                            # end_list.append()
                            count_flight = count_flight + jsjg[0]

        print(count_flight)
        print('=============================')
    # print(count_flight)
    return count_flight


def merge_flight(flight_info):
    merged_flights = []
    grouped_flights = defaultdict(list)
    for flight in flight_info:
        # print(flight)
        flight_no = flight[1]
        if flight[2] is not None:
            legno_prefix = flight[2][0]
            grouped_flights[(flight_no, legno_prefix)].append(flight)
    for (flight_no, legno_prefix), flights in grouped_flights.items():
        if not flights:
            continue

        flights.sort(key=lambda x: x[2])

        merged_flight = flights[0].copy()
        merged_flight[4] = flights[-1][4]
        merged_flight[5] = flights[-1][5]
        final_flight = flights[-1]
        merged_flight[7] = final_flight[7]
        merged_flight[9] = final_flight[9]

        merged_flights.append(merged_flight)
    # for x in merged_flights:
    #     print(x)
    return merged_flights


if __name__ == '__main__':
    year = 2024
    month = 10
    zs = 航段总数量(year, month)
    a = 无联程航班取消数量(year, month)
    b = 计算联程航班(year, month)
    print(a, b, a + b, zs)
    print((a + b) / zs)
    # listA = [['2024-07-13', 'G54628', '100', 'JGN', 'TFU', 'CKG', '2024-07-13 21:15:00', 'ATA', 1, 'None'],
    #          ['2024-07-13', 'G54628', '110', 'TFU', 'CKG', 'CKG', '2024-07-14 01:40:00', 'CNL', 0,
    #           '2024-07-14 01:38:35']]
    # a = merge_flight(listA)
    # print(a)
